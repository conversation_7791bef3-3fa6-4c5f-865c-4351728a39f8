{"name": "instex", "version": "1.0.0", "description": "A lightweight macOS application for editing and previewing LaTeX equations in real-time.", "main": "dist/main/main.js", "homepage": "https://github.com/panyw5/instex#readme", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "tsc -p tsconfig.main.json && electron dist/main/main.js", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:all": "npm run build && electron-builder", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "monaco-editor": "^0.45.0", "katex": "^0.16.9", "mathjs": "^12.2.1", "electron-store": "^8.1.0", "lodash.debounce": "^4.0.8"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/katex": "^0.16.7", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "playwright": "^1.40.1", "prettier": "^3.1.1", "rimraf": "^5.0.5", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vite": "^5.0.10", "vite-plugin-electron": "^0.28.1"}, "build": {"appId": "com.panyw5.instex", "productName": "InsTex", "directories": {"output": "build"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "dmg": {"title": "InsTex", "icon": "assets/icon.icns"}}, "repository": {"type": "git", "url": "git+https://github.com/panyw5/instex.git"}, "keywords": ["latex", "editor", "math", "equations", "katex", "electron"], "author": "panyw5", "license": "MIT", "bugs": {"url": "https://github.com/panyw5/instex/issues"}}