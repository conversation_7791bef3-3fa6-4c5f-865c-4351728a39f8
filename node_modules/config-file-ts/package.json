{"name": "config-file-ts", "version": "0.2.6", "main": "dist/index.js", "types": "dist/index.d.ts", "author": "lee mighdoll", "description": "Use Typescript for configuration files. Types for safety. Compiled for speed.", "keywords": ["typescript", "config", "configuration", "conf", "cli", "cached", "command", "cmd", "command-line"], "dependencies": {"glob": "^10.3.10", "typescript": "^5.3.3"}, "devDependencies": {"@types/chai": "^4.3.11", "@types/glob": "^8.1.0", "@types/jest": "^29.5.11", "@types/node": "^20.10.5", "chai": "^4.3.10", "jest": "^29.7.0", "rimraf": "^5.0.5", "rollup": "^4.9.1", "rollup-plugin-typescript2": "^0.36.0", "ts-jest": "^29.1.1"}, "repository": {"type": "git", "url": "https://github.com/mighdoll/config-file-ts"}, "files": ["dist", "src"], "sideEffects": false, "license": "MIT", "scripts": {"test": "jest", "prepublish": "rimraf dist && rollup -c"}}