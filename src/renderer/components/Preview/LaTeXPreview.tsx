import React, { useEffect, useRef, useState, useMemo } from 'react';
import katex from 'katex';
import 'katex/dist/katex.min.css';
import { debounce } from '../../utils/debounce';

interface LaTeXPreviewProps {
  content: string;
  theme: 'light' | 'dark';
  fontSize?: number;
}

interface RenderError {
  line?: number;
  message: string;
  type: 'error' | 'warning';
}

export const LaTeXPreview: React.FC<LaTeXPreviewProps> = ({
  content,
  theme,
  fontSize = 16,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [errors, setErrors] = useState<RenderError[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Debounced render function
  const debouncedRender = useMemo(
    () => debounce((content: string) => {
      renderLaTeX(content);
    }, 300),
    []
  );

  // Render LaTeX content
  const renderLaTeX = async (latexContent: string) => {
    setIsLoading(true);
    setErrors([]);

    try {
      const html = await processLaTeXContent(latexContent);
      setRenderedContent(html);
    } catch (error) {
      console.error('LaTeX rendering error:', error);
      setErrors([{
        message: error instanceof Error ? error.message : 'Unknown rendering error',
        type: 'error',
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // Process LaTeX content and convert to HTML
  const processLaTeXContent = async (content: string): Promise<string> => {
    if (!content.trim()) {
      return '<div class="empty-content">Start typing LaTeX to see the preview...</div>';
    }

    const lines = content.split('\n');
    const processedLines: string[] = [];
    const currentErrors: RenderError[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNumber = i + 1;

      try {
        const processedLine = await processLine(line, lineNumber);
        processedLines.push(processedLine);
      } catch (error) {
        currentErrors.push({
          line: lineNumber,
          message: error instanceof Error ? error.message : 'Unknown error',
          type: 'error',
        });
        // Add error placeholder
        processedLines.push(`<div class="error-line">Error on line ${lineNumber}: ${line}</div>`);
      }
    }

    if (currentErrors.length > 0) {
      setErrors(currentErrors);
    }

    return processedLines.join('\n');
  };

  // Process individual line
  const processLine = async (line: string, lineNumber: number): Promise<string> => {
    // Skip empty lines and comments
    if (!line.trim() || line.trim().startsWith('%')) {
      return line.startsWith('%') ? `<div class="comment">${line}</div>` : '<br>';
    }

    // Handle display math ($$...$$)
    if (line.includes('$$')) {
      return processDisplayMath(line);
    }

    // Handle inline math ($...$)
    if (line.includes('$') && !line.includes('$$')) {
      return processInlineMath(line);
    }

    // Handle environments
    if (line.includes('\\begin{') || line.includes('\\end{')) {
      return processEnvironment(line);
    }

    // Handle regular text
    return `<div class="text-line">${escapeHtml(line)}</div>`;
  };

  // Process display math
  const processDisplayMath = (line: string): string => {
    const displayMathRegex = /\$\$(.*?)\$\$/g;
    
    return line.replace(displayMathRegex, (match, math) => {
      try {
        const html = katex.renderToString(math.trim(), {
          displayMode: true,
          throwOnError: true,
          strict: false,
        });
        return `<div class="math-display">${html}</div>`;
      } catch (error) {
        return `<div class="math-error">Error rendering: ${math}</div>`;
      }
    });
  };

  // Process inline math
  const processInlineMath = (line: string): string => {
    const inlineMathRegex = /\$([^$]+)\$/g;
    
    const processed = line.replace(inlineMathRegex, (match, math) => {
      try {
        const html = katex.renderToString(math.trim(), {
          displayMode: false,
          throwOnError: true,
          strict: false,
        });
        return `<span class="math-inline">${html}</span>`;
      } catch (error) {
        return `<span class="math-error">Error: ${math}</span>`;
      }
    });

    return `<div class="text-line">${processed}</div>`;
  };

  // Process environments
  const processEnvironment = (line: string): string => {
    // For now, just escape and display as text
    // TODO: Implement proper environment processing
    return `<div class="environment-line">${escapeHtml(line)}</div>`;
  };

  // Escape HTML
  const escapeHtml = (text: string): string => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  // Effect to render content when it changes
  useEffect(() => {
    debouncedRender(content);
    
    return () => {
      debouncedRender.cancel();
    };
  }, [content, debouncedRender]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedRender.cancel();
    };
  }, [debouncedRender]);

  return (
    <div style={{
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: '#374151',
        padding: '8px',
        fontSize: '14px',
        color: 'white',
        flexShrink: 0
      }}>
        LaTeX Preview with KaTeX Rendering
      </div>

      {/* Error display */}
      {errors.length > 0 && (
        <div style={{
          backgroundColor: '#dc2626',
          color: 'white',
          padding: '8px',
          fontSize: '12px',
          flexShrink: 0
        }}>
          <div style={{ fontWeight: 'bold' }}>Rendering Errors:</div>
          {errors.map((error, index) => (
            <div key={index} style={{ marginTop: '4px' }}>
              {error.line && `Line ${error.line}: `}{error.message}
            </div>
          ))}
        </div>
      )}

      {/* Loading indicator */}
      {isLoading && (
        <div style={{
          backgroundColor: '#2563eb',
          color: 'white',
          padding: '8px',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          flexShrink: 0
        }}>
          <div className="spinner" style={{ marginRight: '8px' }}></div>
          Rendering LaTeX...
        </div>
      )}

      {/* Preview content */}
      <div
        ref={containerRef}
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '16px',
          color: theme === 'dark' ? 'white' : 'black',
          backgroundColor: theme === 'dark' ? '#111827' : '#ffffff',
          fontSize: `${fontSize}px`
        }}
        dangerouslySetInnerHTML={{ __html: renderedContent }}
      />
    </div>
  );
};
