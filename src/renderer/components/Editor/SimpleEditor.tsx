import React, { useEffect, useRef } from 'react';
import * as monaco from 'monaco-editor';

interface SimpleEditorProps {
  value: string;
  onChange: (value: string) => void;
  theme: 'light' | 'dark';
}

export const SimpleEditor: React.FC<SimpleEditorProps> = ({
  value,
  onChange,
  theme,
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const monacoRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);

  useEffect(() => {
    if (!editorRef.current) return;

    // Create a simple Monaco editor without custom language
    monacoRef.current = monaco.editor.create(editorRef.current, {
      value,
      language: 'plaintext', // Start with plaintext instead of custom LaTeX
      theme: theme === 'dark' ? 'vs-dark' : 'vs',
      fontSize: 14,
      lineNumbers: 'on',
      minimap: { enabled: false },
      wordWrap: 'on',
      automaticLayout: true,
      scrollBeyondLastLine: false,
    });

    // Set up change listener
    const disposable = monacoRef.current.onDidChangeModelContent(() => {
      const newValue = monacoRef.current?.getValue() || '';
      onChange(newValue);
    });

    return () => {
      disposable.dispose();
      monacoRef.current?.dispose();
    };
  }, []);

  // Update theme
  useEffect(() => {
    if (monacoRef.current) {
      monaco.editor.setTheme(theme === 'dark' ? 'vs-dark' : 'vs');
    }
  }, [theme]);

  // Update value
  useEffect(() => {
    if (monacoRef.current && value !== monacoRef.current.getValue()) {
      monacoRef.current.setValue(value);
    }
  }, [value]);

  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-gray-700 p-2 text-sm text-white flex-shrink-0">
        Simple Monaco Editor (PlainText Mode)
      </div>
      <div
        ref={editorRef}
        className="flex-1 w-full"
      />
    </div>
  );
};
