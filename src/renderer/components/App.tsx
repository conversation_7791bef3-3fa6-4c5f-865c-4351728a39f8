import React, { useState, useEffect, useCallback } from 'react';
// import { SplitPane } from './Layout/SplitPane';
// import { LaTeXEditor } from './Editor/LaTeXEditor';
// import { LaTeXPreview } from './Preview/LaTeXPreview';
// import { Toolbar } from './Layout/Toolbar';
// import { StatusBar } from './Layout/StatusBar';

interface AppState {
  content: string;
  filePath: string | null;
  isDirty: boolean;
  theme: 'light' | 'dark';
  previewVisible: boolean;
  splitDirection: 'horizontal' | 'vertical';
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    content: '% Welcome to InsTex!\n% Start typing your LaTeX equations here\n\n\\begin{equation}\n  E = mc^2\n\\end{equation}\n\n\\begin{align}\n  \\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n  \\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}',
    filePath: null,
    isDirty: false,
    theme: 'dark',
    previewVisible: true,
    splitDirection: 'horizontal',
  });

  // Simple test version with Tailwind CSS
  return (
    <div style={{ height: '100vh', backgroundColor: '#111827', color: 'white', display: 'flex', flexDirection: 'column' }}>
      <div style={{ backgroundColor: '#1f2937', padding: '16px', borderBottom: '1px solid #374151' }}>
        <h1 style={{ fontSize: '20px', fontWeight: 'bold', margin: 0 }}>InsTex - LaTeX Editor</h1>
        <p style={{ color: '#9ca3af', margin: '4px 0 0 0' }}>Development Test Version with Tailwind</p>
      </div>

      <div style={{ flex: 1, padding: '16px' }}>
        <div style={{ backgroundColor: '#1f2937', padding: '16px', borderRadius: '8px', marginBottom: '16px' }}>
          <h2 style={{ fontSize: '18px', marginBottom: '8px' }}>Application Status</h2>
          <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
            <li style={{ marginBottom: '4px' }}>✅ React App Loaded</li>
            <li style={{ marginBottom: '4px' }}>✅ Basic Styling Working</li>
            <li style={{ marginBottom: '4px' }}>✅ TypeScript Compiled</li>
            <li style={{ marginBottom: '4px' }}>🔄 Testing Tailwind CSS...</li>
          </ul>
        </div>

        <div className="bg-gray-800 p-4 rounded">
          <h3 className="text-lg mb-2">Tailwind CSS Test</h3>
          <p className="text-green-400">If you can see this green text, Tailwind CSS is working!</p>
        </div>

        <div style={{ backgroundColor: '#1f2937', padding: '16px', borderRadius: '8px', marginTop: '16px' }}>
          <h3 style={{ fontSize: '16px', marginBottom: '8px' }}>Sample LaTeX Content:</h3>
          <pre style={{
            fontSize: '12px',
            color: '#10b981',
            backgroundColor: '#111827',
            padding: '8px',
            borderRadius: '4px',
            overflow: 'auto',
            whiteSpace: 'pre-wrap'
          }}>
            {state.content}
          </pre>
        </div>
      </div>

      <div style={{ backgroundColor: '#2563eb', color: 'white', padding: '8px', fontSize: '14px' }}>
        Status: Development Mode | Theme: {state.theme} | Ready
      </div>
    </div>
  );
};

export default App;
