import React, { useState, useEffect, useCallback } from 'react';
import { SplitPane } from './Layout/SplitPane';
import { LaTeXEditor } from './Editor/LaTeXEditor';
import { LaTeXPreview } from './Preview/LaTeXPreview';
import { Toolbar } from './Layout/Toolbar';
import { StatusBar } from './Layout/StatusBar';

interface AppState {
  content: string;
  filePath: string | null;
  isDirty: boolean;
  theme: 'light' | 'dark';
  previewVisible: boolean;
  splitDirection: 'horizontal' | 'vertical';
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    content: '% Welcome to InsTex!\n% Start typing your LaTeX equations here\n\n\\begin{equation}\n  E = mc^2\n\\end{equation}\n\n\\begin{align}\n  \\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n  \\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}',
    filePath: null,
    isDirty: false,
    theme: 'dark',
    previewVisible: true,
    splitDirection: 'horizontal',
  });

  // Handle content changes
  const handleContentChange = useCallback((newContent: string) => {
    setState(prev => ({
      ...prev,
      content: newContent,
      isDirty: true,
    }));
  }, []);

  // Handle file operations
  const handleNewFile = useCallback(() => {
    setState(prev => ({
      ...prev,
      content: '',
      filePath: null,
      isDirty: false,
    }));
  }, []);

  const handleOpenFile = useCallback(async () => {
    try {
      const result = await window.electronAPI.openFile();
      if (result.success && result.content !== undefined) {
        setState(prev => ({
          ...prev,
          content: result.content!,
          filePath: result.filePath || null,
          isDirty: false,
        }));
      }
    } catch (error) {
      console.error('Failed to open file:', error);
    }
  }, []);

  const handleSaveFile = useCallback(async () => {
    try {
      const result = await window.electronAPI.saveFile(state.content, state.filePath || undefined);
      if (result.success) {
        setState(prev => ({
          ...prev,
          filePath: result.filePath || prev.filePath,
          isDirty: false,
        }));
      }
    } catch (error) {
      console.error('Failed to save file:', error);
    }
  }, [state.content, state.filePath]);

  const handleSaveAsFile = useCallback(async () => {
    try {
      const result = await window.electronAPI.saveFileAs(state.content);
      if (result.success) {
        setState(prev => ({
          ...prev,
          filePath: result.filePath || null,
          isDirty: false,
        }));
      }
    } catch (error) {
      console.error('Failed to save file as:', error);
    }
  }, [state.content]);

  // Handle theme toggle
  const handleToggleTheme = useCallback(() => {
    setState(prev => ({
      ...prev,
      theme: prev.theme === 'dark' ? 'light' : 'dark',
    }));
  }, []);

  // Handle preview toggle
  const handleTogglePreview = useCallback(() => {
    setState(prev => ({
      ...prev,
      previewVisible: !prev.previewVisible,
    }));
  }, []);

  // Handle copy LaTeX
  const handleCopyLatex = useCallback(async () => {
    try {
      await window.electronAPI.writeToClipboard(state.content);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  }, [state.content]);

  // Handle split direction toggle
  const handleToggleSplitDirection = useCallback(() => {
    setState(prev => ({
      ...prev,
      splitDirection: prev.splitDirection === 'horizontal' ? 'vertical' : 'horizontal',
    }));
  }, []);

  // Set up menu event listeners
  useEffect(() => {
    const handleMenuAction = (action: string) => {
      switch (action) {
        case 'menu:new-file':
          handleNewFile();
          break;
        case 'menu:open-file':
          handleOpenFile();
          break;
        case 'menu:save-file':
          handleSaveFile();
          break;
        case 'menu:save-as':
          handleSaveAsFile();
          break;
        case 'menu:copy-latex':
          handleCopyLatex();
          break;
        case 'menu:toggle-preview':
          handleTogglePreview();
          break;
        case 'menu:toggle-theme':
          handleToggleTheme();
          break;
      }
    };

    window.electronAPI.onMenuAction(handleMenuAction);

    return () => {
      window.electronAPI.removeMenuListeners();
    };
  }, [
    handleNewFile,
    handleOpenFile,
    handleSaveFile,
    handleSaveAsFile,
    handleCopyLatex,
    handleTogglePreview,
    handleToggleTheme,
  ]);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      <Toolbar
        onNewFile={handleNewFile}
        onOpenFile={handleOpenFile}
        onSaveFile={handleSaveFile}
        onToggleTheme={handleToggleTheme}
        onTogglePreview={handleTogglePreview}
        onToggleSplitDirection={handleToggleSplitDirection}
        theme={state.theme}
        previewVisible={state.previewVisible}
        splitDirection={state.splitDirection}
      />
      
      <div className="flex-1 overflow-hidden">
        {state.previewVisible ? (
          <SplitPane direction={state.splitDirection}>
            <div className="flex-1">
              <LaTeXEditor
                value={state.content}
                onChange={handleContentChange}
                theme={state.theme}
              />
            </div>
            <div className="flex-1">
              <LaTeXPreview
                content={state.content}
                theme={state.theme}
              />
            </div>
          </SplitPane>
        ) : (
          <LaTeXEditor
            value={state.content}
            onChange={handleContentChange}
            theme={state.theme}
          />
        )}
      </div>
      
      <StatusBar
        filePath={state.filePath}
        isDirty={state.isDirty}
        lineCount={state.content.split('\n').length}
        theme={state.theme}
      />
    </div>
  );
};

export default App;
