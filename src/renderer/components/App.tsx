import React, { useState, useEffect, useCallback } from 'react';
import { SimpleEditor } from './Editor/SimpleEditor';
import { LaTeXEditor } from './Editor/LaTeXEditor';
import { LaTeXPreview } from './Preview/LaTeXPreview';
// import { SplitPane } from './Layout/SplitPane';
// import { Toolbar } from './Layout/Toolbar';
// import { StatusBar } from './Layout/StatusBar';

interface AppState {
  content: string;
  filePath: string | null;
  isDirty: boolean;
  theme: 'light' | 'dark';
  previewVisible: boolean;
  splitDirection: 'horizontal' | 'vertical';
  useLatexEditor: boolean;
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    content: '% Welcome to InsTex!\n% Start typing your LaTeX equations here\n\n\\begin{equation}\n  E = mc^2\n\\end{equation}\n\n\\begin{align}\n  \\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n  \\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}',
    filePath: null,
    isDirty: false,
    theme: 'dark',
    previewVisible: true,
    splitDirection: 'horizontal',
    useLatexEditor: false, // Start with simple editor
  });

  const handleContentChange = useCallback((newContent: string) => {
    setState(prev => ({
      ...prev,
      content: newContent,
      isDirty: true,
    }));
  }, []);

  const toggleLatexMode = useCallback(() => {
    setState(prev => ({
      ...prev,
      useLatexEditor: !prev.useLatexEditor,
    }));
  }, []);

  const toggleEditorType = useCallback(() => {
    setState(prev => ({
      ...prev,
      useLatexEditor: !prev.useLatexEditor,
    }));
  }, []);

  // Test version with explicit inline styles to override any CSS conflicts
  return (
    <div style={{
      height: '100vh',
      backgroundColor: '#111827',
      color: 'white',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{
        backgroundColor: '#1f2937',
        padding: '16px',
        borderBottom: '1px solid #374151',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h1 style={{ fontSize: '20px', fontWeight: 'bold', margin: 0 }}>InsTex - LaTeX Editor</h1>
          <p style={{ color: '#9ca3af', margin: '4px 0 0 0' }}>
            {state.useLatexEditor ? 'LaTeX Mode with Syntax Highlighting' : 'Plain Text Mode'}
          </p>
        </div>
        <button
          onClick={toggleLatexMode}
          style={{
            backgroundColor: state.useLatexEditor ? '#10b981' : '#6b7280',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '6px',
            border: 'none',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500'
          }}
        >
          {state.useLatexEditor ? '✓ LaTeX Mode' : 'Enable LaTeX Mode'}
        </button>
      </div>

      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        overflow: 'hidden'
      }}>
        {/* Left side - Editor */}
        <div style={{
          width: '50%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {state.useLatexEditor ? (
            <LaTeXEditor
              value={state.content}
              onChange={handleContentChange}
              theme={state.theme}
            />
          ) : (
            <SimpleEditor
              value={state.content}
              onChange={handleContentChange}
              theme={state.theme}
            />
          )}
        </div>

        {/* Vertical divider */}
        <div style={{
          width: '4px',
          backgroundColor: '#374151',
          height: '100%'
        }}></div>

        {/* Right side - Preview placeholder */}
        <div style={{
          width: '50%',
          height: '100%',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div style={{
            backgroundColor: '#1f2937',
            padding: '16px',
            borderRadius: '8px',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <h3 style={{ fontSize: '18px', marginBottom: '8px' }}>
              {state.useLatexEditor ? 'LaTeX Preview' : 'Content Preview'}
            </h3>
            <div style={{ fontSize: '14px', color: '#9ca3af', marginBottom: '16px' }}>
              {state.useLatexEditor
                ? 'LaTeX equations will be rendered here'
                : 'Raw content display - Enable LaTeX mode for rendering'
              }
            </div>
            {state.useLatexEditor ? (
              <LaTeXPreview
                content={state.content}
                theme={state.theme}
              />
            ) : (
              <div style={{
                backgroundColor: '#111827',
                padding: '8px',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#10b981',
                overflow: 'auto',
                flex: 1
              }}>
                <strong>Current Content:</strong><br/>
                <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>
                  {state.content || 'No content yet...'}
                </pre>
              </div>
            )}
          </div>
        </div>
      </div>

      <div style={{
        backgroundColor: '#2563eb',
        color: 'white',
        padding: '8px',
        fontSize: '14px'
      }}>
        Status: Testing Monaco Editor | Characters: {state.content.length} | Theme: {state.theme}
      </div>
    </div>
  );
};

export default App;
