import React, { useState, useEffect, useCallback } from 'react';
import { LaTeXEditor } from './Editor/LaTeXEditor';
import { LaTeXPreview } from './Preview/LaTeXPreview';
// import { SplitPane } from './Layout/SplitPane';
// import { Toolbar } from './Layout/Toolbar';
// import { StatusBar } from './Layout/StatusBar';

interface AppState {
  content: string;
  filePath: string | null;
  isDirty: boolean;
  theme: 'light' | 'dark';
  previewVisible: boolean;
  splitDirection: 'horizontal' | 'vertical';
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    content: '% Welcome to InsTex!\n% Start typing your LaTeX equations here\n\n\\begin{equation}\n  E = mc^2\n\\end{equation}\n\n\\begin{align}\n  \\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n  \\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}',
    filePath: null,
    isDirty: false,
    theme: 'dark',
    previewVisible: true,
    splitDirection: 'horizontal',
  });

  const handleContentChange = useCallback((newContent: string) => {
    setState(prev => ({
      ...prev,
      content: newContent,
      isDirty: true,
    }));
  }, []);

  const toggleEditorType = useCallback(() => {
    setState(prev => ({
      ...prev,
      useLatexEditor: !prev.useLatexEditor,
    }));
  }, []);

  // Test version with explicit inline styles to override any CSS conflicts
  return (
    <div style={{
      height: '100vh',
      backgroundColor: '#111827',
      color: 'white',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{
        backgroundColor: '#1f2937',
        padding: '16px',
        borderBottom: '1px solid #374151'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: '20px', fontWeight: 'bold', margin: 0 }}>InsTex - LaTeX Editor</h1>
            <p style={{ color: '#9ca3af', margin: '4px 0 0 0' }}>
              LaTeX Editor with Syntax Highlighting & Real-time Preview
            </p>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={() => handleContentChange('\\alpha + \\beta = \\gamma')}
              style={{
                backgroundColor: '#374151',
                color: 'white',
                padding: '6px 12px',
                borderRadius: '4px',
                border: 'none',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Greek Letters
            </button>
            <button
              onClick={() => handleContentChange('\\frac{a}{b} + \\sqrt{c} = \\int_{0}^{1} f(x) dx')}
              style={{
                backgroundColor: '#374151',
                color: 'white',
                padding: '6px 12px',
                borderRadius: '4px',
                border: 'none',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Fractions & Integrals
            </button>
            <button
              onClick={() => handleContentChange('\\begin{equation}\nE = mc^2\n\\end{equation}\n\n\\begin{align}\n\\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n\\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}')}
              style={{
                backgroundColor: '#374151',
                color: 'white',
                padding: '6px 12px',
                borderRadius: '4px',
                border: 'none',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Equations
            </button>
          </div>
        </div>
      </div>

      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'row',
        overflow: 'hidden'
      }}>
        {/* Left side - LaTeX Editor */}
        <div style={{
          width: '50%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <LaTeXEditor
            value={state.content}
            onChange={handleContentChange}
            theme={state.theme}
          />
        </div>

        {/* Vertical divider */}
        <div style={{
          width: '4px',
          backgroundColor: '#374151',
          height: '100%'
        }}></div>

        {/* Right side - LaTeX Preview */}
        <div style={{
          width: '50%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <LaTeXPreview
            content={state.content}
            theme={state.theme}
          />
        </div>
      </div>

      <div style={{
        backgroundColor: '#2563eb',
        color: 'white',
        padding: '8px',
        fontSize: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          Status: Ready | Characters: {state.content.length} | Lines: {state.content.split('\n').length}
        </div>
        <div>
          LaTeX Mode | Theme: {state.theme} | {state.isDirty ? 'Modified' : 'Saved'}
        </div>
      </div>
    </div>
  );
};

export default App;
