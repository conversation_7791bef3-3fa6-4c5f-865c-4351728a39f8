import React, { useState, useEffect, useCallback } from 'react';
import { SimpleEditor } from './Editor/SimpleEditor';
import { LaTeXEditor } from './Editor/LaTeXEditor';
import { LaTeXPreview } from './Preview/LaTeXPreview';
// import { SplitPane } from './Layout/SplitPane';
// import { Toolbar } from './Layout/Toolbar';
// import { StatusBar } from './Layout/StatusBar';

interface AppState {
  content: string;
  filePath: string | null;
  isDirty: boolean;
  theme: 'light' | 'dark';
  previewVisible: boolean;
  splitDirection: 'horizontal' | 'vertical';
  useLatexEditor: boolean;
}

const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    content: '% Welcome to InsTex!\n% Start typing your LaTeX equations here\n\n\\begin{equation}\n  E = mc^2\n\\end{equation}\n\n\\begin{align}\n  \\nabla \\times \\vec{E} &= -\\frac{\\partial \\vec{B}}{\\partial t} \\\\\n  \\nabla \\times \\vec{B} &= \\mu_0\\vec{J} + \\mu_0\\epsilon_0\\frac{\\partial \\vec{E}}{\\partial t}\n\\end{align}',
    filePath: null,
    isDirty: false,
    theme: 'dark',
    previewVisible: true,
    splitDirection: 'horizontal',
    useLatexEditor: false, // Start with simple editor
  });

  const handleContentChange = useCallback((newContent: string) => {
    setState(prev => ({
      ...prev,
      content: newContent,
      isDirty: true,
    }));
  }, []);

  const toggleEditorType = useCallback(() => {
    setState(prev => ({
      ...prev,
      useLatexEditor: !prev.useLatexEditor,
    }));
  }, []);

  // Test version with Monaco Editor
  return (
    <div className="h-screen bg-gray-900 text-white flex flex-col">
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <h1 className="text-xl font-bold">InsTex - LaTeX Editor</h1>
        <p className="text-gray-400">Testing Monaco Editor Integration</p>
      </div>

      <div className="flex-1 flex">
        {/* Left side - Editor */}
        <div className="w-1/2 border-r border-gray-700">
          <SimpleEditor
            value={state.content}
            onChange={handleContentChange}
            theme={state.theme}
          />
        </div>

        {/* Right side - Preview placeholder */}
        <div className="w-1/2 p-4">
          <div className="bg-gray-800 p-4 rounded h-full">
            <h3 className="text-lg mb-2">Preview Area</h3>
            <div className="text-sm text-gray-400 mb-4">
              Monaco Editor Test - Type in the editor to see changes here
            </div>
            <div className="bg-gray-900 p-2 rounded text-xs text-green-400 overflow-auto">
              <strong>Current Content:</strong><br/>
              {state.content || 'No content yet...'}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-600 text-white p-2 text-sm">
        Status: Testing Monaco Editor | Characters: {state.content.length} | Theme: {state.theme}
      </div>
    </div>
  );
};

export default App;
